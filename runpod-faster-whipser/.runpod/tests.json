{"tests": [{"name": "basic_test", "input": {"audio": "https://github.com/runpod-workers/sample-inputs/raw/main/audio/gettysburg.wav", "model": "turbo"}, "timeout": 10000}, {"name": "multi_audio", "input": {"audios": ["https://github.com/runpod-workers/sample-inputs/raw/main/audio/gettysburg.wav", "https://github.com/runpod-workers/sample-inputs/raw/main/audio/NM014.mp3", "https://github.com/runpod-workers/sample-inputs/raw/main/audio/Arthur.mp3", "https://github.com/runpod-workers/sample-inputs/raw/main/audio/accent.mp3"], "model": "turbo", "word_timestamps": true, "enable_vad": true}, "timeout": 10000}], "config": {"gpuTypeId": "NVIDIA GeForce RTX 4090", "gpuCount": 1, "allowedCudaVersions": ["12.7", "12.6", "12.5", "12.4", "12.3"]}}
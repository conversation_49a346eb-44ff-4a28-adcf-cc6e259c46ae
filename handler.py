"""
RunPod Serverless Handler for Whisper Diarization
Simple, functional implementation for RunPod deployment
"""

import os
import torch
import runpod

# Import modules
from lib.audio_utils import get_wave_file
from lib.transcribe import Transcribe
from lib.merge_cosine import Merge
from lib.speaker_profile_handler import SpeakerProfileHandler

class WhisperDiarizationHandler:
    def __init__(self):
        """Initialize the handler"""
        print("Initializing Whisper Diarization Handler...")

        # Check CUDA availability
        print(f"CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA device: {torch.cuda.get_device_name()}")
            print(f"CUDA version: {torch.version.cuda}")
        else:
            print("WARNING: CUDA not available, using CPU")

        # Check HuggingFace token
        hf_token = os.environ.get("HUGGINGFACE_TOKEN")
        if not hf_token:
            print("WARNING: HUGGINGFACE_TOKEN not set!")
            print("Set it as environment variable for pyannote models")

        # Initialize components
        self.transcribe = Transcribe()
        self.merge = Merge()
        self.speaker_handler = SpeakerProfileHandler()
        print("Handler initialized!")

    def process(self, job_input):
        """Process the job input"""
        try:
            # Extract parameters with defaults
            num_speakers = job_input.get("num_speakers")
            translate = job_input.get("translate", False)
            language = job_input.get("language")
            prompt = job_input.get("prompt", "")
            vad_filter = job_input.get("vad_filter", True)
            normalize_audio = job_input.get("normalize_audio", True)
            diarize_only = job_input.get("diarize_only", False)
            enable_merge = job_input.get("enable_merge", False)
            known_speakers = job_input.get("known_speakers")

            # Get and process audio file using audio_utils
            file_string = job_input.get("file_string")
            file_url = job_input.get("file_url")
            file_path = job_input.get("file")

            wave_filename = get_wave_file(file_string, file_url, file_path, normalize_audio)

            # Initialize results
            whisper_segments = []
            diarization_segments = []
            merged_segments = []
            transcript_info = {"language": language}

            # Step 1: Transcription
            if not diarize_only:
                print("Starting transcription...")
                whisper_segments, transcript_info = self.transcribe.speech_to_text(
                    wave_filename, num_speakers, prompt, language, translate, vad_filter
                )
                print(f"Transcription done: {len(whisper_segments)} segments")

            # Step 2: Diarization
            print("Starting diarization...")
            from lib.diarization_v1 import DiarizationProcessor
            diarization_processor = DiarizationProcessor()

            parsed_speakers = self.speaker_handler.parse_known_speakers(known_speakers)
            diarization_segments, detected_num_speakers, _, speaker_emb_map = \
                diarization_processor.process(wave_filename, num_speakers, existing_embeddings=parsed_speakers)

            print(f"Diarization done: {detected_num_speakers} speakers")

            # Step 3: Merge (optional)
            if enable_merge and whisper_segments:
                print("Merging results...")
                merged_segments = self.merge.process(whisper_segments, diarization_segments, speaker_emb_map)

            # Format results
            speaker_results = self.speaker_handler.format_speaker_results(speaker_emb_map)

            # Build output
            output = {
                "whisper_segments": whisper_segments,
                "diarization_segments": diarization_segments,
                "language": transcript_info.get("language"),
                "num_speakers": detected_num_speakers,
                "speaker_embeddings": speaker_results
            }

            if enable_merge:
                output["merged_segments"] = merged_segments

            return output

        except Exception as e:
            print(f"Error: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"error": str(e)}



# Initialize handler
handler = WhisperDiarizationHandler()

def runpod_handler(job):
    """RunPod serverless handler function"""
    job_input = job.get("input", {})
    return handler.process(job_input)

if __name__ == "__main__":
    print("Starting RunPod Whisper Diarization Service...")
    runpod.serverless.start({"handler": runpod_handler})
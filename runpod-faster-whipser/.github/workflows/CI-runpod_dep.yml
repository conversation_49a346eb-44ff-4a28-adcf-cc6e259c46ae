name: CI | Update runpod package version

on:
  repository_dispatch:
    types: [python-package-release]

  push:
    branches:
      - "main"
      - "master"

  workflow_dispatch:

jobs:
  check_dep:
    runs-on: ubuntu-latest
    name: Check python requirements file and update
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Check for new package version and update
        run: |
          echo "Fetching the current runpod version from requirements.txt..."

          # Get current version, allowing for '~=' operator
          current_version=$(grep -oP 'runpod~=\K[^ ]+' ./builder/requirements.txt)
          echo "Current version: $current_version"

          # Get new version from PyPI
          new_version=$(curl -s https://pypi.org/pypi/runpod/json | jq -r .info.version)
          echo "NEW_VERSION_ENV=$new_version" >> $GITHUB_ENV
          echo "New version: $new_version"

          if [ -z "$new_version" ]; then
              echo "ERROR: Failed to fetch the new version from PyPI."
              exit 1
          fi

          # Extract major and minor from current version (e.g., 1.7)
          current_major_minor=$(echo $current_version | cut -d. -f1,2)
          new_major_minor=$(echo $new_version | cut -d. -f1,2)

          echo "Current major.minor: $current_major_minor"
          echo "New major.minor: $new_major_minor"

          # Check if the new version is within the current major.minor range (e.g., 1.7.x)
          if [ "$new_major_minor" = "$current_major_minor" ]; then
              echo "No update needed. The new version ($new_version) is within the allowed range (~= $current_major_minor)."
              exit 0
          fi

          echo "New major/minor detected ($new_major_minor). Updating runpod version..."

          # Update requirements.txt with the new version while keeping '~='
          sed -i "s/runpod~=.*/runpod~=$new_version/" ./builder/requirements.txt
          echo "requirements.txt has been updated."

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v3
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: Update runpod package version
          title: Update runpod package version
          body: The package version has been updated to ${{ env.NEW_VERSION_ENV }}
          branch: runpod-package-update

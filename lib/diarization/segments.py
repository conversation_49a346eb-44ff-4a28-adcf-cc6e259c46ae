
import torch
from typing import List, Dict

class Segmenter:
    """
    Handles audio segmentation using Silero VAD model to detect speech segments.
    """
    def __init__(self):
        try:
            # Load Silero VAD model
            self.vad_model, utils = torch.hub.load(
                "snakers4/silero-vad",
                "silero_vad",
                force_reload=True
            )
            self.get_speech_timestamps = utils[0]  # First item in utils tuple is get_speech_ts
            
        except Exception as e:
            raise RuntimeError(f"Failed to initialize Silero VAD model: {e}")

    def get_active_speech_timestamps(self, 
                     waveform: torch.Tensor,
                     sample_rate: int = 16000,
                     return_seconds: bool = True,
                     min_segment_duration: float = 0.4) -> List[Dict[str, float]]:
        """
        Segments audio using Silero VAD model to detect speech segments.
        Merges segments shorter than min_segment_duration with the previous segment.
        
        Args:
            waveform (torch.Tensor): Audio waveform tensor
            sample_rate (int): Audio sample rate (default: 16000)
            return_seconds (bool): If True, returns timestamps in seconds, otherwise in samples
            min_segment_duration (float): Minimum segment duration in seconds (default: 0.4)
            
        Returns:
            List[Dict[str, float]]: List of segments with 'start' and 'end' timestamps
        """
        try:
            # Ensure waveform is float
            waveform = waveform.float()
            
            # Get speech timestamps
            speech_timestamps = self.get_speech_timestamps(
                waveform[0],  # Take first channel if stereo
                self.vad_model,
                return_seconds=return_seconds
            )
            
            # Merge short segments with previous segments
            merged_timestamps = []
            for i, segment in enumerate(speech_timestamps):
                duration = segment['end'] - segment['start']
                
                if duration < min_segment_duration and i > 0:
                    # Extend the previous segment's end time
                    merged_timestamps[-1]['end'] = segment['end']
                else:
                    segment['duration'] = duration
                    merged_timestamps.append(segment)
            
            return merged_timestamps
            
        except Exception as e:
            raise RuntimeError(f"Failed to segment audio: {e}")



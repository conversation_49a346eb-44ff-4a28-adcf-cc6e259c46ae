# lib/speaker_profile_handler.py
from typing import Dict, List, Optional, Union
from pydantic import BaseModel
import numpy as np
import json

class SpeakerProfile(BaseModel):
    name: str
    embedding: List[float]
    confidence: Optional[float] = None
    metadata: Optional[Dict] = None

class KnownSpeakers(BaseModel):
    speakers: Dict[str, SpeakerProfile]

class SpeakerProfileHandler:
    def __init__(self):
        self.known_speakers = None
        self.parsed_speakers = None

    def parse_known_speakers(self, known_speakers_json: str) -> Dict:
        """
        Parses and validates known speaker profiles from a JSON string.
        """
        if not known_speakers_json:
            return None

        try:
            speakers_data = json.loads(known_speakers_json)
            self.known_speakers = KnownSpeakers(**speakers_data)
            self.parsed_speakers = {
                speaker_id: {
                    "embedding": np.array(profile.embedding),
                    "name": profile.name,
                    "confidence": profile.confidence,
                    "metadata": profile.metadata
                }
                for speaker_id, profile in self.known_speakers.speakers.items()
            }
            return self.parsed_speakers

        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON format for known_speakers: {e}")
        except Exception as e:
            raise ValueError(f"Error processing known_speakers: {e}")

    def format_speaker_results(self, speaker_emb_map: Dict) -> Dict:
        """
        Formats the speaker assignment results.
        """
        speaker_results = {}
        for speaker_id, embedding in speaker_emb_map.items():
            speaker_info = {
                "embedding": embedding,
                "matched_profile": None,
                "confidence": None,
                "name": None,
                "metadata": None
            }
            if self.parsed_speakers and speaker_id in self.parsed_speakers:
                known_data = self.parsed_speakers[speaker_id]
                speaker_info.update({
                    "matched_profile": speaker_id,
                    "name": known_data["name"],
                    "confidence": known_data.get("confidence"),
                    "metadata": known_data.get("metadata")
                })
            speaker_results[speaker_id] = speaker_info
        return speaker_results

    def get_example_format(self) -> str:
        return """
        {
            "speakers": {
                "speaker_id1": {
                    "name": "John Doe",
                    "embedding": [0.1, 0.2, ...],
                    "confidence": 0.95,
                    "metadata": {"age": 30, "gender": "male"}
                },
                "speaker_id2": {
                    "name": "Jane Smith",
                    "embedding": [0.3, 0.4, ...],
                    "confidence": 0.92
                }
            }
        }
        """

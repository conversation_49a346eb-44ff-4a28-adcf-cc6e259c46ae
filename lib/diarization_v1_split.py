# lib/diarization_v2.py
import collections
from collections import defaultdict
import numpy as np
from sklearn.cluster import DBSCAN
from sklearn.metrics.pairwise import cosine_distances
import os
from pyannote.audio import Pipeline
import torch
import torchaudio
from typing import Dict, List, Tuple, Any, Optional
from lib.diarization.segments import Segmenter
from lib.audio_utils import split_audio_from_segments

class SpeakerLabelGenerator:
    def __init__(self):
        self.speakers = {}
        self.labels = []
        self.next_speaker = ord("A")
        self.count = 0

    def get(self, name):
        if name not in self.speakers:
            current = chr(self.next_speaker)
            self.speakers[name] = current
            self.labels.append(current)
            self.next_speaker += 1
            self.count += 1
        return self.speakers[name]

    def get_all(self):
        return self.labels

class SpeakerEmbedding:
    def __init__(self, embedding_dimension=768):
        self.dimension = embedding_dimension

    def process(self, embedding, speaker_label):
        if speaker_label == "UNKNOWN":
            return np.zeros(self.dimension)

        if isinstance(embedding, torch.Tensor):
            embedding = embedding.numpy()

        if np.isnan(embedding).any():
            return None

        norm = np.linalg.norm(embedding)
        if norm == 0:
            return None
        
        normalized = embedding / norm
        return None if np.isnan(normalized).any() else normalized

class SpeakerAssignment:
    def __init__(self, threshold=0.3):
        self.threshold = threshold
        self.clustering = DBSCAN(eps=0.4, min_samples=2, metric='precomputed')

    def match_with_existing(self, embedding, known_embeddings):
        best_match = None
        best_score = float('inf')
        
        for speaker_id, known_embedding in known_embeddings.items():
            distance = cosine_distances([embedding], [known_embedding])[0][0]
            if distance < best_score and distance < self.threshold:
                best_score = distance
                best_match = speaker_id
        
        return best_match

    def cluster_speakers(self, embeddings):
        if not embeddings:
            return []
        distance_matrix = cosine_distances(np.vstack(embeddings))
        return self.clustering.fit_predict(distance_matrix)

class DiarizationProcessor:
    def __init__(self):
        self.debug = True
        self.labels = SpeakerLabelGenerator()
        self.embedding_processor = SpeakerEmbedding()
        self.speaker_assigner = SpeakerAssignment()
        self.setup_pipeline()

    def setup_pipeline(self):
        token = os.environ.get("HUGGINGFACE_TOKEN", "*************************************")
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.diarization = Pipeline.from_pretrained(
            "pyannote/speaker-diarization-3.1",
            use_auth_token=token
        ).to(self.device)
        self.diarization.instantiate({
            "clustering": {"method": "centroid", "min_cluster_size": 12, "threshold": 0.7045654963945799},
            "segmentation": {"min_duration_off": 0.0}
        })

    def process(self, file_path, num_speakers=None, existing_embeddings=None):
        # Load and segment audio
        waveform, sample_rate = torchaudio.load(file_path)
        
        # Get speech segments using VAD
        segmenter = Segmenter()
        speech_timestamps = segmenter.get_active_speech_timestamps(waveform, sample_rate)
        self.debug and print(f"Found {len(speech_timestamps)} active speech segments")
        
        # Split audio into segments
        loaded_audio_segments = split_audio_from_segments(waveform, sample_rate, speech_timestamps)
        if not loaded_audio_segments:
            print("No audio segments found for processing")
            return None, 0, [], {}

        # Process each segment
        all_speaker_segments = []
        all_embeddings = []
        segment_boundaries = []  # Track segment boundaries for later

        for segment_waveform, sample_rate, identifier, segment in loaded_audio_segments:
            self.debug and print(f"Processing segment {identifier}/{len(loaded_audio_segments)-1}")
            
            # Process segment with diarization pipeline
            with torch.no_grad():
                diarization, embedding = self.diarization(
                    {"waveform": segment_waveform, "sample_rate": sample_rate},
                    return_embeddings=True,
                    num_speakers=num_speakers
                )

            # Store segment boundary
            segment_boundaries.append({
                'start': segment['start'],
                'end': segment['end'],
                'identifier': identifier
            })

            # Extract speaker turns and embeddings
            for turn, _, speaker in diarization.itertracks(yield_label=True):
                processed_embedding = self.embedding_processor.process(embedding, speaker)
                if processed_embedding is not None:
                    # Adjust timestamps relative to original audio
                    absolute_start = segment['start'] + turn.start
                    absolute_end = segment['start'] + turn.end
                    
                    all_speaker_segments.append({
                        'start': absolute_start,
                        'end': absolute_end,
                        'local_speaker': speaker,
                        'identifier': identifier,
                        'segment_start': segment['start'],
                        'segment_end': segment['end']
                    })
                    all_embeddings.append(processed_embedding)

        if not all_speaker_segments:
            print("No valid speaker segments found")
            return None, 0, [], {}

        # Cluster all embeddings together before any merging
        if all_embeddings:
            clusters = self.speaker_assigner.cluster_speakers(all_embeddings)
            
            # Create initial speaker mapping from clusters
            initial_speaker_mapping = {}
            for idx, (segment, cluster) in enumerate(zip(all_speaker_segments, clusters)):
                if cluster != -1:  # Valid cluster
                    initial_speaker_mapping[segment['local_speaker']] = f"Speaker_{cluster}"

            # Add debug information
            if self.debug:
                for idx, (seg, cluster) in enumerate(zip(all_speaker_segments, clusters)):
                    print(f"Segment {seg['identifier']}: {seg['start']:.2f}s - {seg['end']:.2f}s -> Cluster {cluster}")

        # Now merge segments within each audio chunk while respecting cluster assignments
        MIN_DURATION = 0.35
        filtered_segments = []
        
        # Group segments by audio chunk
        segments_by_chunk = defaultdict(list)
        for idx, segment in enumerate(all_speaker_segments):
            segments_by_chunk[segment['identifier']].append((idx, segment))

        # Process each chunk separately while maintaining global speaker assignments
        for chunk_id, chunk_segments in segments_by_chunk.items():
            if not chunk_segments:
                continue

            current_idx, current = chunk_segments[0]
            current = current.copy()
            
            for next_idx, next_seg in chunk_segments[1:]:
                segment_duration = next_seg['end'] - next_seg['start']
                current_speaker = initial_speaker_mapping.get(current['local_speaker'])
                next_speaker = initial_speaker_mapping.get(next_seg['local_speaker'])
                
                if segment_duration < MIN_DURATION:
                    # For short segments, use cluster information to decide
                    if current_speaker == next_speaker:
                        current['end'] = next_seg['end']
                        continue
                
                if current_speaker == next_speaker:
                    current['end'] = next_seg['end']
                else:
                    filtered_segments.append(current)
                    current = next_seg.copy()
            
            filtered_segments.append(current)

        # Now match with existing embeddings if provided
        final_speaker_mapping = {}
        if existing_embeddings:
            for local_speaker in initial_speaker_mapping:
                # Get all embeddings for this speaker
                speaker_embeddings = [emb for idx, emb in enumerate(all_embeddings) 
                                    if all_speaker_segments[idx]['local_speaker'] == local_speaker]
                
                if speaker_embeddings:
                    # Use mean embedding for matching
                    mean_embedding = np.mean(speaker_embeddings, axis=0)
                    match = self.speaker_assigner.match_with_existing(mean_embedding, existing_embeddings)
                    if match:
                        final_speaker_mapping[local_speaker] = match
                    else:
                        final_speaker_mapping[local_speaker] = initial_speaker_mapping[local_speaker]
        else:
            final_speaker_mapping = initial_speaker_mapping

        # Create final segments with consistent speaker labels
        final_segments = [
            {
                'start': seg['start'],
                'end': seg['end'],
                'speaker': final_speaker_mapping.get(seg['local_speaker'], "UNKNOWN")
            }
            for seg in all_speaker_segments
        ]

        # Create speaker embedding map
        speaker_emb_map = defaultdict(list)
        for idx, seg in enumerate(all_speaker_segments):
            speaker = final_speaker_mapping.get(seg['local_speaker'])
            if speaker and all_embeddings[idx] is not None:
                embedding = np.asarray(all_embeddings[idx]).reshape(-1)
                if embedding.shape[0] == self.embedding_processor.dimension:
                    speaker_emb_map[speaker].append(embedding)

        # Calculate mean embeddings
        final_emb_map = {}
        for speaker, embs in speaker_emb_map.items():
            if embs:
                try:
                    stacked_embs = np.vstack(embs)
                    final_emb_map[speaker] = np.mean(stacked_embs, axis=0)
                except ValueError as e:
                    print(f"Warning: Could not process embeddings for {speaker}: {e}")
                    continue

        num_speakers = len(set(s['speaker'] for s in final_segments))
        speaker_labels = sorted(set(s['speaker'] for s in final_segments))

        return final_segments, num_speakers, speaker_labels, final_emb_map

    def _assign_speakers(self, embeddings, speaker_segments, existing_embeddings=None):
        """Handle speaker assignment using existing embeddings and clustering"""
        speaker_mapping = {}
        
        # Match with existing embeddings first
        if existing_embeddings:
            for idx, emb in enumerate(embeddings):
                local_speaker = speaker_segments[idx]['local_speaker']
                if local_speaker in speaker_mapping:
                    continue
                    
                match = self.speaker_assigner.match_with_existing(emb, existing_embeddings)
                if match:
                    speaker_mapping[local_speaker] = match

        # Handle unmatched speakers with clustering
        unmatched = [(idx, seg) for idx, seg in enumerate(speaker_segments) 
                    if seg['local_speaker'] not in speaker_mapping]
        
        if unmatched:
            unmatched_embeddings = [embeddings[idx] for idx, _ in unmatched]
            clusters = self.speaker_assigner.cluster_speakers(unmatched_embeddings)
            
            for (idx, seg), cluster in zip(unmatched, clusters):
                if cluster != -1:
                    speaker_mapping[seg['local_speaker']] = f"Speaker_{cluster}"

        return speaker_mapping

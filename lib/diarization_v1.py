class SpeakerLabelGenerator:
    def __init__(self):
        self.speakers = {}
        self.labels = []
        self.next_speaker = ord("A")
        self.count = 0

    def get(self, name):
        if name not in self.speakers:
            current = chr(self.next_speaker)
            self.speakers[name] = current
            self.labels.append(current)
            self.next_speaker += 1
            self.count += 1
        return self.speakers[name]

    def get_all(self):
        return self.labels


from pyannote.audio import Pipeline
import torch
import torchaudio
from typing import Dict, Optional, Tuple, Any
import numpy as np
import os

class DiarizationProcessor:
    def __init__(self):
        self.labels = None
        self.merge = None
        self.diarization = None
        self.setup_diarization()

    def setup_diarization(self):
        """Initialize the diarization pipeline"""
        token = os.environ.get("HUGGINGFACE_TOKEN", "*************************************")
        self.diarization = Pipeline.from_pretrained(
            "pyannote/speaker-diarization-3.1",
            use_auth_token=token,
        )

        # self.diarization.instantiate({
        #     "clustering": {
        #         "method": "centroid",
        #         "min_cluster_size": 10,
        #         "threshold": 0.85,
        #     },
        #     "segmentation": {
        #         "min_duration_off": 0.35,
        #     }
        # })
        self.diarization.to( torch.device("cuda" if torch.cuda.is_available() else "cpu"))

    def perform_diarization(self, waveform: torch.Tensor, sample_rate: int, num_speakers: int = None) -> Tuple[Any, np.ndarray]:
        """
        Perform diarization on the audio input
        
        Args:
            waveform: Audio waveform tensor
            sample_rate: Audio sample rate
            num_speakers: Optional number of speakers
            
        Returns:
            Tuple of (diarization_result, embeddings)
        """
        return self.diarization(
            {"waveform": waveform, "sample_rate": sample_rate},
            return_embeddings=True,
            num_speakers=num_speakers
        )

    def format(self, diarization):
        segments = []
        for segment, _, speaker in diarization.itertracks(yield_label=True):
            segments.append(
                {
                    "speaker": self.labels.get(speaker),
                    "start": segment.start,
                    "end": segment.end
                }
            )
        return segments

    def process(self, file_path, num_speakers: int = None, existing_embeddings: Optional[Dict] = None):
        # 1) load audio
        waveform, sample_rate = torchaudio.load(file_path)

        # 2) reset label generator
        self.labels = SpeakerLabelGenerator()

        # 3) perform diarization
        diarization, embeddings = self.perform_diarization(waveform, sample_rate, num_speakers)

        # 4) format raw turns into a list of dicts
        raw_segments = []
        for turn, _, speaker_id in diarization.itertracks(yield_label=True):
            raw_segments.append({
                "speaker": self.labels.get(speaker_id),
                "start": turn.start,
                "end":   turn.end
            })

        # 5) merge consecutive segments with the same speaker
        merged = []
        for seg in raw_segments:
            if not merged:
                merged.append(seg.copy())
            else:
                last = merged[-1]
                if seg["speaker"] == last["speaker"]:
                    # extend the end time
                    last["end"] = seg["end"]
                else:
                    merged.append(seg.copy())

        # 6) drop any “illusion” segments before the first real one ≥ 0.4s
        for idx, seg in enumerate(merged):
            if (seg["end"] - seg["start"]) >= 0.4:
                # keep from this segment onward
                merged = merged[idx:]
                # reset its start to zero
                merged[0]["start"] = 0.0
                break

        # 7) snap each segment’s end to the next segment’s start
        for i in range(len(merged) - 1):
            merged[i]["end"] = merged[i + 1]["start"]

        # 8) return
        unique_speakers = self.labels.count
        return merged, unique_speakers, self.labels.get_all(), {}


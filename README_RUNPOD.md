# RunPod Whisper Diarization

Audio transcribing + diarization pipeline optimized for RunPod deployment.

## Changes from Replicate Version

### New Output Structure
- **`whisper_segments`**: Raw Whisper transcription results
- **`diarization_segments`**: Raw speaker diarization results  
- **`merged_segments`**: Combined results (only when `enable_merge=true`)
- **`language`**: Detected language
- **`num_speakers`**: Number of detected speakers
- **`speaker_embeddings`**: Speaker embedding information

### New Parameters
- **`enable_merge`** (boolean, default: `false`): Enable/disable merging of Whisper and diarization results

## Deployment on RunPod

### Prerequisites
1. RunPod account
2. HuggingFace token with access to:
   - [pyannote/segmentation-3.0](https://hf.co/pyannote/segmentation-3.0)
   - [pyannote/speaker-diarization-3.1](https://hf.co/pyannote/speaker-diarization-3.1)

### Steps
1. **Set HuggingFace Token**:
   ```bash
   # Add your token to handler.py or set as environment variable
   export HUGGINGFACE_TOKEN="your_token_here"
   ```

2. **Build Docker Image**:
   ```bash
   docker build -t whisper-diarization-runpod .
   ```

3. **Push to Registry**:
   ```bash
   # Tag and push to your container registry
   docker tag whisper-diarization-runpod your-registry/whisper-diarization-runpod
   docker push your-registry/whisper-diarization-runpod
   ```

4. **Deploy on RunPod**:
   - Create new template with your Docker image
   - Set GPU requirements (recommended: RTX 4090 or better)
   - Configure environment variables if needed

## API Usage

### Input Parameters
```json
{
  "file_string": "base64_encoded_audio",
  "file_url": "https://example.com/audio.wav",
  "num_speakers": 2,
  "translate": false,
  "language": "en",
  "enable_merge": false,
  "debug_info": false
}
```

### Output Format
```json
{
  "whisper_segments": [...],
  "diarization_segments": [...],
  "merged_segments": [...],  // Only if enable_merge=true
  "language": "en",
  "num_speakers": 2,
  "speaker_embeddings": {...}
}
```

## Key Differences from Replicate

| Feature | Replicate Version | RunPod Version |
|---------|------------------|----------------|
| **Output Structure** | Single `segments` field with merged results | Separate `whisper_segments`, `diarization_segments`, and optional `merged_segments` |
| **Merge Behavior** | Always enabled | Optional via `enable_merge` parameter (default: `false`) |
| **Framework** | Cog (BasePredictor) | RunPod Serverless |
| **Container** | cog.yaml configuration | Dockerfile |
| **Deployment** | `cog push` to Replicate | Docker build/push to RunPod |
| **API Format** | Cog Input/Output classes | JSON input/output |
| **Flexibility** | Fixed output format | Granular control over returned data |

### Benefits of RunPod Version:
1. **Separate Results**: Access raw Whisper and diarization outputs independently
2. **Optional Merging**: Choose whether to merge results or not
3. **Better Debugging**: Raw outputs available for analysis
4. **Cost Control**: More deployment options and pricing models
5. **Flexibility**: Easier to customize and extend

## Testing Locally

Before deploying to RunPod, you can test the handler locally:

```bash
# Set your HuggingFace token
export HUGGINGFACE_TOKEN="your_token_here"

# Run local test
python3 test_runpod_locally.py
```

## Example API Call

```python
import requests
import base64

# Encode your audio file
with open("audio.wav", "rb") as f:
    audio_base64 = base64.b64encode(f.read()).decode('utf-8')

# API request
response = requests.post("https://api.runpod.ai/v2/YOUR_ENDPOINT_ID/runsync",
    headers={"Authorization": "Bearer YOUR_API_KEY"},
    json={
        "input": {
            "file_string": audio_base64,
            "num_speakers": 2,
            "enable_merge": False,  # Get separate results
            "language": "en",
            "translate": False
        }
    }
)

result = response.json()
print(f"Whisper segments: {len(result['output']['whisper_segments'])}")
print(f"Diarization segments: {len(result['output']['diarization_segments'])}")
```

## AI/ML Models Used
- Whisper Large v3 Turbo (CTranslate 2 version `faster-whisper==1.1.1`)
- Pyannote audio 3.3.1

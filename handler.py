# handler.py - <PERSON><PERSON><PERSON> Handler for Whisper Diarization
import base64
import datetime
import os
import time
import json
from typing import Dict, List, Optional
import runpod

# Import modules
from lib.audio_utils import get_wave_file
from lib.transcribe import Transcribe
from lib.merge_cosine import Merge
from lib.speaker_profile_handler import SpeakerProfileHandler

class WhisperDiarizationHandler:
    def __init__(self):
        """Initialize the handler with models"""
        print("Initializing Whisper Diarization Handler...")

        # Check for HuggingFace token
        hf_token = os.environ.get("HUGGINGFACE_TOKEN")
        if not hf_token or hf_token == "":
            print("WARNING: HUGGINGFACE_TOKEN not set. Please set it as an environment variable.")
            print("You need access to pyannote/segmentation-3.0 and pyannote/speaker-diarization-3.1")

        self.transcribe = Transcribe()
        self.merge = Merge()
        self.speaker_handler = SpeakerProfileHandler()
        print("Handler initialized successfully!")

    def process_request(self, job):
        """Process a RunPod job request"""
        try:
            job_input = job.get("input", {})
            
            # Extract parameters from job input
            file_string = job_input.get("file_string")
            file_url = job_input.get("file_url")
            file_path = job_input.get("file")  # For local files
            
            num_speakers = job_input.get("num_speakers")
            translate = job_input.get("translate", False)
            language = job_input.get("language")
            prompt = job_input.get("prompt", "")
            vad_filter = job_input.get("vad_filter", True)
            normalize_audio = job_input.get("normalize_audio", True)
            diarize_only = job_input.get("diarize_only", False)
            known_speakers = job_input.get("known_speakers")
            debug_info = job_input.get("debug_info", False)
            
            # NEW: Parameter to enable/disable merging (default: False)
            enable_merge = job_input.get("enable_merge", False)
            
            # Process audio file
            wave_filename = get_wave_file(file_string, file_url, file_path, normalize_audio)
            
            # Initialize results
            whisper_segments = []
            diarization_segments = []
            merged_segments = []
            transcript_info = {"language": language}
            
            # Step 1: Transcription (Whisper)
            if not diarize_only:
                print("Starting Whisper transcription...")
                whisper_segments, transcript_info = self.transcribe.speech_to_text(
                    wave_filename,
                    num_speakers,
                    prompt,
                    language,
                    translate,
                    vad_filter
                )
                print(f"Whisper transcription completed: {len(whisper_segments)} segments")
            
            # Step 2: Diarization
            print("Starting diarization...")
            time_diarization_start = time.time()
            
            # Initialize diarization processor
            from lib.diarization_v1 import DiarizationProcessor
            diarization_processor = DiarizationProcessor()

            # Process diarization
            parsed_speakers = self.speaker_handler.parse_known_speakers(known_speakers)

            diarization_segments, detected_num_speakers, _, speaker_emb_map = \
                diarization_processor.process(
                    wave_filename,
                    num_speakers,
                    existing_embeddings=parsed_speakers
                )
            
            time_diarization_end = time.time()
            print(f"Diarization completed in {time_diarization_end - time_diarization_start:.5f} seconds")
            print(f"Detected {detected_num_speakers} speakers")
            
            # Step 3: Merge (only if enabled)
            if enable_merge and whisper_segments:
                print("Starting merge process...")
                time_merging_start = time.time()
                merged_segments = self.merge.process(whisper_segments, diarization_segments, speaker_emb_map)
                time_merging_end = time.time()
                print(f"Merge completed in {time_merging_end - time_merging_start:.5f} seconds")
            
            # Format speaker results
            speaker_results = self.speaker_handler.format_speaker_results(speaker_emb_map)
            
            # Prepare output
            output = {
                "whisper_segments": whisper_segments,
                "diarization_segments": diarization_segments,
                "language": transcript_info.get("language"),
                "num_speakers": detected_num_speakers,
                "speaker_embeddings": speaker_results
            }
            
            # Add merged segments only if merging was enabled
            if enable_merge:
                output["merged_segments"] = merged_segments
            
            # Add debug info if requested
            if debug_info:
                output["debug_info"] = {
                    "raw_whisper": whisper_segments,
                    "raw_diarization": diarization_segments,
                    "transcript_info": transcript_info
                }
            
            print("Processing completed successfully!")
            return output
            
        except Exception as e:
            print(f"Error processing request: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"error": str(e)}

# Initialize handler
handler = WhisperDiarizationHandler()

def runpod_handler(job):
    """RunPod handler function"""
    return handler.process_request(job)

if __name__ == "__main__":
    print("Starting RunPod Whisper Diarization Service...")
    runpod.serverless.start({"handler": runpod_handler})

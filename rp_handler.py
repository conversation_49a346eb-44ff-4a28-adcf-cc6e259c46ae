"""
RunPod Handler for Whisper Diarization Service

This handler provides a RunPod Serverless compatible interface for the Whisper Diarization service.
It includes input validation, debugging utilities, and proper cleanup.
"""

import base64
import logging
import tempfile
import time
import os
from typing import Dict, List, Optional

from rp_schema import INPUT_VALIDATIONS
from runpod.serverless.utils import download_files_from_urls, rp_cleanup, rp_debugger
from runpod.serverless.utils.rp_validator import validate
import runpod

# Import modules
from lib.audio_utils import get_wave_file
from lib.transcribe import Transcribe
from lib.merge_cosine import Merge
from lib.speaker_profile_handler import SpeakerProfileHandler

logger = logging.getLogger("runpod-worker")

class WhisperDiarizationPredictor:
    def __init__(self):
        """Initialize the predictor with models"""
        print("Initializing Whisper Diarization Predictor...")
        
        # Check for HuggingFace token
        hf_token = os.environ.get("HUGGINGFACE_TOKEN")
        if not hf_token or hf_token == "":
            print("WARNING: HUGGINGFACE_TOKEN not set. Please set it as an environment variable.")
            print("You need access to pyannote/segmentation-3.0 and pyannote/speaker-diarization-3.1")

        self.transcribe = Transcribe()
        self.merge = Merge()
        self.speaker_handler = SpeakerProfileHandler()
        print("Predictor initialized successfully!")

    def predict(self, **kwargs):
        """
        Run prediction with the given parameters
        
        Returns:
        dict: The result containing whisper_segments, diarization_segments, and optionally merged_segments
        """
        # Extract parameters
        file_string = kwargs.get("file_string")
        file_url = kwargs.get("file_url") 
        file_path = kwargs.get("file")
        
        num_speakers = kwargs.get("num_speakers")
        translate = kwargs.get("translate", False)
        language = kwargs.get("language")
        prompt = kwargs.get("prompt", "")
        vad_filter = kwargs.get("vad_filter", True)
        normalize_audio = kwargs.get("normalize_audio", True)
        diarize_only = kwargs.get("diarize_only", False)
        known_speakers = kwargs.get("known_speakers")
        debug_info = kwargs.get("debug_info", False)
        enable_merge = kwargs.get("enable_merge", False)
        
        # Process audio file
        wave_filename = get_wave_file(file_string, file_url, file_path, normalize_audio)
        
        # Initialize results
        whisper_segments = []
        diarization_segments = []
        merged_segments = []
        transcript_info = {"language": language}
        
        # Step 1: Transcription (Whisper)
        if not diarize_only:
            print("Starting Whisper transcription...")
            whisper_segments, transcript_info = self.transcribe.speech_to_text(
                wave_filename,
                num_speakers,
                prompt,
                language,
                translate,
                vad_filter
            )
            print(f"Whisper transcription completed: {len(whisper_segments)} segments")
        
        # Step 2: Diarization
        print("Starting diarization...")
        time_diarization_start = time.time()
        
        # Initialize diarization processor
        from lib.diarization_v1 import DiarizationProcessor
        diarization_processor = DiarizationProcessor()

        # Process diarization
        parsed_speakers = self.speaker_handler.parse_known_speakers(known_speakers)

        diarization_segments, detected_num_speakers, _, speaker_emb_map = \
            diarization_processor.process(
                wave_filename,
                num_speakers,
                existing_embeddings=parsed_speakers
            )
        
        time_diarization_end = time.time()
        print(f"Diarization completed in {time_diarization_end - time_diarization_start:.5f} seconds")
        print(f"Detected {detected_num_speakers} speakers")
        
        # Step 3: Merge (only if enabled)
        if enable_merge and whisper_segments:
            print("Starting merge process...")
            time_merging_start = time.time()
            merged_segments = self.merge.process(whisper_segments, diarization_segments, speaker_emb_map)
            time_merging_end = time.time()
            print(f"Merge completed in {time_merging_end - time_merging_start:.5f} seconds")
        
        # Format speaker results
        speaker_results = self.speaker_handler.format_speaker_results(speaker_emb_map)
        
        # Prepare output
        output = {
            "whisper_segments": whisper_segments,
            "diarization_segments": diarization_segments,
            "language": transcript_info.get("language"),
            "num_speakers": detected_num_speakers,
            "speaker_embeddings": speaker_results
        }
        
        # Add merged segments only if merging was enabled
        if enable_merge:
            output["merged_segments"] = merged_segments
        
        # Add debug info if requested
        if debug_info:
            output["debug_info"] = {
                "raw_whisper": whisper_segments,
                "raw_diarization": diarization_segments,
                "transcript_info": transcript_info
            }
        
        print("Processing completed successfully!")
        return output

# Initialize the model
MODEL = WhisperDiarizationPredictor()

def base64_to_tempfile(base64_file: str) -> str:
    """
    Convert base64 file to tempfile.
    
    Parameters:
    base64_file (str): Base64 file
    
    Returns:
    str: Path to tempfile
    """
    with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
        temp_file.write(base64.b64decode(base64_file))
    
    return temp_file.name

@rp_debugger.FunctionTimer
def run_whisper_diarization_job(job):
    """
    Run inference on the Whisper Diarization model.
    
    Parameters:
    job (dict): Input job containing the model parameters
    
    Returns:
    dict: The result of the prediction
    """
    job_input = job['input']
    
    with rp_debugger.LineTimer('validation_step'):
        input_validation = validate(job_input, INPUT_VALIDATIONS)
        
        if 'errors' in input_validation:
            return {"error": input_validation['errors']}
        job_input = input_validation['validated_input']
    
    # Validate that exactly one audio input method is provided
    audio_fields = [
        bool(job_input.get('file_string', False)),
        bool(job_input.get('file_url', False)),
        bool(job_input.get('file', False))
    ]
    if sum(audio_fields) == 0:
        return {'error': 'Must provide one of file_string, file_url, or file'}
    if sum(audio_fields) > 1:
        return {'error': 'Must provide only one of file_string, file_url, or file'}
    
    # Handle different audio input methods
    if job_input.get('file_url'):
        with rp_debugger.LineTimer('download_step'):
            downloaded_file = download_files_from_urls(job['id'], [job_input['file_url']])[0]
            job_input['file'] = downloaded_file
            job_input['file_url'] = None  # Clear URL since we now have local file
    elif job_input.get('file_string'):
        # Convert base64 to temp file
        temp_file = base64_to_tempfile(job_input['file_string'])
        job_input['file'] = temp_file
        job_input['file_string'] = None  # Clear base64 since we now have local file
    
    logger.info(f"Processing audio file: {job_input.get('file', 'unknown')}")
    
    try:
        with rp_debugger.LineTimer('prediction_step'):
            result = MODEL.predict(**job_input)
        
        with rp_debugger.LineTimer('cleanup_step'):
            rp_cleanup.clean(['input_objects'])
        
        return result
        
    except Exception as e:
        logger.error(f"Error processing request: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

# Start the RunPod serverless handler
runpod.serverless.start({"handler": run_whisper_diarization_job})

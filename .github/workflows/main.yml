name: Push to Replicate

on:
  workflow_dispatch:
    inputs:
      model_name:
        required: true
        description: "thomasmol/whisper-diarization"

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Free Disk Space (Ubuntu)
        uses: jlumbroso/free-disk-space@main
        with:
          # this might remove tools that are actually needed,
          # if set to "true" but frees about 6 GB
          tool-cache: false
          android: true
          dotnet: true
          haskell: true
          large-packages: true
          docker-images: true
          swap-storage: true

      - name: Check out code
        uses: actions/checkout@v3

      - name: Setup Cog
        uses: replicate/setup-cog@v1.0.2
        with:
          token: ${{ secrets.REPLICATE_API_TOKEN }}

      - name: Push to Replicate
        run: |
          cog push r8.im/${{ inputs.model_name }}

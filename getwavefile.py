import lib.audio_utils as audio_utils

def get_wave_file(file_string, file_url, file, normalize_audio=False, use_cuda=False):
    return audio_utils.get_wave_file(file_string, file_url, file, normalize_audio, use_cuda)

if __name__ == "__main__":
    file_url = "https://n8n.kinote.copex.io/share/workflows/EzOODKkjPqWODhAU/executions/temp/binary_data/f773dbc0-d959-4ca4-a9cf-59b59695ba69.m4a?token=k2kllai9283kslfj983woweio9282kdiwo"

    wave_file = get_wave_file(None, file_url, None, True)
    print(wave_file)
    exit(0)
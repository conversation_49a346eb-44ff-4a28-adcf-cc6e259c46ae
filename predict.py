# predict.py
import base64
import datetime
import os
import time
from typing import Dict, List, Optional
from cog import BasePredictor, BaseModel, Input, Path

# Import modules
from lib.audio_utils import get_wave_file
from lib.transcribe import Transcribe
from lib.merge_cosine import Merge
from lib.speaker_profile_handler import SpeakerProfileHandler

class SpeakerProfile(BaseModel):
    name: str
    embedding: List[float]
    confidence: Optional[float] = None
    metadata: Optional[Dict] = None

class KnownSpeakers(BaseModel):
    speakers: Dict[str, SpeakerProfile]

class Output(BaseModel):
    segments: list
    language: str = None
    num_speakers: int = None
    debug_info: list = []
    speaker_embeddings: Optional[Dict[str, Dict]] = None

class Predictor(BasePredictor):

    def setup(self):
        self.transcribe = Transcribe()
        self.merge = Merge()
        self.speaker_handler = SpeakerProfileHandler()

    def predict(
        self,
        file_string: str = Input(
            description="Provide a Base64 encoded audio file.", default=None
        ),
        file_url: str = Input(
            description="Or provide a direct audio file URL.", default=None
        ),
        file: Path = Input(description="Or provide an audio file.", default=None),
        num_speakers: int = Input(
            description="Number of speakers, leave empty to autodetect.",
            ge=1,
            le=50,
            default=None,
        ),
        translate: bool = Input(
            description="Translate the speech into English.", default=False
        ),
        language: str = Input(
            description="Language of the spoken words as a language code like 'en'. Leave empty to auto-detect language.",
            default=None,
        ),
        prompt: str = Input(
            description="Vocabulary: provide names, acronyms, and loanwords in a list. Use punctuation for best accuracy.",
            default=None,
        ),
        normalize_audio: bool = Input(
            description="Normalize audio level using loudnorm filter.", default=True
        ),
        vad_filter: bool = Input(
            description="Enable VAD filter.", default=True
        ),
        diarize_v2: bool = Input(
            description="Diarization Model V2.", default=False
        ),
        diarize_only: bool = Input(
            description="Diarization Only", default=False
        ),
        debug_info: bool = Input(
            description="Enable Debug Info.", default=False
        ),
        known_speakers: str = Input(
            description="""JSON string containing known speaker profiles. Format:
            {
                "speakers": {
                    "speaker_id1": {
                        "name": "John Doe",
                        "embedding": [0.1, 0.2, ...],
                        "confidence": 0.95,
                        "metadata": {"age": 30, "gender": "male"}
                    },
                    "speaker_id2": {
                        "name": "Jane Smith",
                        "embedding": [0.3, 0.4, ...],
                        "confidence": 0.92
                    }
                }
            }""",
            default=None
        ),
    ) -> Output:
        try:
            wave_filename = get_wave_file(file_string, file_url, file, normalize_audio)
            if not diarize_only:
                segments, transcript_info = self.transcribe.speech_to_text(
                    wave_filename,
                    num_speakers,
                    prompt,
                    language,
                    translate,
                    vad_filter
                )
            else:
                segments = []
                transcript_info = {"language": language}

            time_merging_start = time.time()
            print("Starting diarization")
            time_diarization_start = time.time()

            # Initialize appropriate diarization processor
            if diarize_v2:
                from lib.diarization_v2 import DiarizationProcessor
            else:
                from lib.diarization_v1 import DiarizationProcessor

            diarization_processor = DiarizationProcessor()

            # Process diarization
            parsed_speakers = self.speaker_handler.parse_known_speakers(known_speakers)
            
            if diarize_v2:
                diarize_segments, detected_num_speakers, speaker_labels, speaker_emb_map = \
                    diarization_processor.process(
                        wave_filename, 
                        num_speakers,
                        existing_embeddings=parsed_speakers
                    )
            else:
                diarize_segments, detected_num_speakers, speaker_labels, speaker_emb_map = \
                    diarization_processor.process(
                        wave_filename, 
                        num_speakers,
                        existing_embeddings=parsed_speakers
                    )

            time_diarization_end = time.time()
            print(f"Finished diarization in {time_diarization_end - time_diarization_start:.5f} seconds")

            # Merge segments
            merged_segments = self.merge.process(segments, diarize_segments, speaker_emb_map)
            time_merging_end = time.time()
            print(f"Finished merging in {time_merging_end - time_merging_start:.5f} seconds")

            print("Done with inference")
            speaker_results = self.speaker_handler.format_speaker_results(speaker_emb_map)

            return Output(
                segments=merged_segments,
                language=transcript_info.language,
                num_speakers=detected_num_speakers,
                speaker_embeddings=speaker_results,
                debug_info=[
                    segments, 
                    transcript_info,
                    diarize_segments
                ] if debug_info else []
            )

        except Exception as e:
            raise RuntimeError("Error running inference with local model", e)

        finally:
            if os.path.exists(wave_filename):
                os.remove(wave_filename)

    def convert_time(self, secs, offset_seconds=0):
        return datetime.timedelta(seconds=(round(secs) + offset_seconds))

# Whisper Diarization - RunPod Serverless (Simple)

Einfache, funktionale RunPod Serverless Implementierung für Whisper + Diarization.

## 🚀 Schnellstart

### 1. Environment Setup
```bash
export HUGGINGFACE_TOKEN="your_huggingface_token"
```

### 2. Build & Deploy
```bash
./deploy.sh
```

### 3. Push to Registry
```bash
# Ändere "your-registry" in deploy.sh zu deiner Registry
docker push your-registry/whisper-diarization:latest
```

### 4. RunPod Endpoint erstellen
- Gehe zu [RunPod Console](https://www.runpod.io/console/serverless)
- Erstelle neuen Endpoint mit deinem Image
- Setze `HUGGINGFACE_TOKEN` als Environment Variable
- Empfohlen: RTX 4090 oder A100 GPU

## 📁 Dateien

- `handler.py` - RunPod Handler (einfach & funktional)
- `Dockerfile` - Optimiert für RunPod Serverless
- `deploy.sh` - Build & Deploy Script
- `test_input.json` - Test Input
- `lib/` - Core Funktionalität (unverändert)

## 🔧 API Usage

### Input
```json
{
  "input": {
    "file_url": "https://example.com/audio.wav",
    "num_speakers": 2,
    "language": "en",
    "enable_merge": true
  }
}
```

### Output
```json
{
  "whisper_segments": [...],
  "diarization_segments": [...],
  "merged_segments": [...],
  "language": "en",
  "num_speakers": 2,
  "speaker_embeddings": {...}
}
```

## 📋 Parameter

| Parameter | Type | Default | Beschreibung |
|-----------|------|---------|--------------|
| `file_url` | string | - | Audio URL |
| `file_string` | string | - | Base64 Audio |
| `file` | string | - | Lokaler Pfad |
| `num_speakers` | int | auto | Anzahl Speaker |
| `language` | string | auto | Sprache |
| `translate` | bool | false | Zu Englisch übersetzen |
| `enable_merge` | bool | false | Whisper + Diarization mergen |
| `diarize_only` | bool | false | Nur Diarization |
| `vad_filter` | bool | true | VAD Filter |
| `normalize_audio` | bool | true | Audio normalisieren |

## 🧪 Lokaler Test

```bash
# Build
docker build -t whisper-diarization .

# Run
docker run --rm -p 8000:8000 \
  -e HUGGINGFACE_TOKEN=$HUGGINGFACE_TOKEN \
  whisper-diarization

# Test
curl -X POST http://localhost:8000/runsync \
  -H "Content-Type: application/json" \
  -d @test_input.json
```

## ⚡ Performance

- **GPU**: RTX 4090 oder A100 empfohlen
- **VRAM**: 16GB+ für optimale Performance
- **Disk**: 20GB+ für Modelle
- **Cold Start**: 30-60s beim ersten Request

Das war's! Einfach, funktional, wartbar. 🎉

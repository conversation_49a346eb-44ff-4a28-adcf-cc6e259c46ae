# Whisper Diarization - RunPod Serverless

This directory contains the RunPod Serverless implementation of the Whisper Diarization service. It provides audio transcription with speaker diarization capabilities, optimized for RunPod's serverless infrastructure.

## Features

- **Audio Transcription**: Using OpenAI Whisper for high-quality speech-to-text
- **Speaker Diarization**: Using pyannote.audio for speaker identification and segmentation
- **Flexible Input**: Support for audio URLs, base64 encoded files, or local files
- **Optional Merging**: Combine transcription and diarization results
- **RunPod Optimized**: Built with RunPod serverless utilities for validation, debugging, and cleanup

## Files

- `rp_handler.py` - Main RunPod serverless handler
- `rp_schema.py` - Input validation schema
- `Dockerfile.runpod` - Docker configuration for RunPod serverless
- `test_input.json` - Example input for testing
- `deploy_runpod_serverless.sh` - Deployment script
- `runpod_requirements.txt` - Python dependencies

## Quick Start

### 1. Prerequisites

- Docker installed
- RunPod account and API key
- Hugging<PERSON><PERSON> token with access to pyannote models

### 2. Environment Setup

```bash
export RUNPOD_API_KEY="your_runpod_api_key"
export HUGGINGFACE_TOKEN="your_huggingface_token"
```

### 3. Build and Deploy

```bash
./deploy_runpod_serverless.sh
```

### 4. Test Locally

```bash
docker build -f Dockerfile.runpod -t whisper-diarization-runpod .
docker run --rm -p 8000:8000 -e HUGGINGFACE_TOKEN=$HUGGINGFACE_TOKEN whisper-diarization-runpod
```

## API Usage

### Input Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `file_url` | string | No | None | URL to audio file |
| `file_string` | string | No | None | Base64 encoded audio |
| `file` | string | No | None | Local file path |
| `num_speakers` | integer | No | None | Number of speakers (auto-detect if None) |
| `translate` | boolean | No | false | Translate to English |
| `language` | string | No | None | Language code (auto-detect if None) |
| `prompt` | string | No | "" | Initial prompt for Whisper |
| `vad_filter` | boolean | No | true | Enable VAD filtering |
| `normalize_audio` | boolean | No | true | Normalize audio |
| `diarize_only` | boolean | No | false | Skip transcription |
| `enable_merge` | boolean | No | false | Merge whisper and diarization |
| `debug_info` | boolean | No | false | Include debug information |
| `known_speakers` | object | No | None | Known speaker embeddings |

### Example Request

```json
{
  "input": {
    "file_url": "https://example.com/audio.wav",
    "num_speakers": 2,
    "language": "en",
    "enable_merge": true,
    "vad_filter": true
  }
}
```

### Example Response

```json
{
  "whisper_segments": [
    {
      "start": 0.0,
      "end": 5.0,
      "text": "Hello, how are you today?",
      "words": [...]
    }
  ],
  "diarization_segments": [
    {
      "start": 0.0,
      "end": 5.0,
      "speaker": "SPEAKER_00"
    }
  ],
  "merged_segments": [
    {
      "start": 0.0,
      "end": 5.0,
      "text": "Hello, how are you today?",
      "speaker": "SPEAKER_00"
    }
  ],
  "language": "en",
  "num_speakers": 2,
  "speaker_embeddings": {...}
}
```

## Deployment to RunPod

### 1. Build and Push Image

```bash
# Build the image
docker build -f Dockerfile.runpod -t whisper-diarization-runpod .

# Tag for your registry
docker tag whisper-diarization-runpod your-registry/whisper-diarization-runpod

# Push to registry
docker push your-registry/whisper-diarization-runpod
```

### 2. Create RunPod Endpoint

1. Go to [RunPod Serverless Console](https://www.runpod.io/console/serverless)
2. Click "New Endpoint"
3. Configure:
   - **Container Image**: `your-registry/whisper-diarization-runpod`
   - **Environment Variables**:
     - `HUGGINGFACE_TOKEN`: Your HuggingFace token
   - **GPU**: RTX 4090 or A100 recommended
   - **Container Disk**: 20GB minimum
   - **Timeout**: 300 seconds recommended

### 3. Test Your Endpoint

```bash
curl -X POST https://api.runpod.ai/v2/YOUR_ENDPOINT_ID/runsync \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d @test_input.json
```

## Performance Notes

- **GPU Requirements**: RTX 4090 or A100 recommended for optimal performance
- **Memory**: 16GB+ VRAM recommended for larger models
- **Disk Space**: 20GB+ for model storage
- **Cold Start**: First request may take 30-60 seconds for model loading

## Troubleshooting

### Common Issues

1. **HuggingFace Token Error**: Ensure your token has access to pyannote models
2. **GPU Memory Error**: Use smaller batch sizes or upgrade GPU
3. **Audio Format Error**: Ensure audio is in supported format (WAV, MP3, etc.)

### Debug Mode

Enable debug information in your request:

```json
{
  "input": {
    "file_url": "...",
    "debug_info": true
  }
}
```

## Support

For issues specific to this RunPod implementation, please check:
- RunPod documentation
- pyannote.audio requirements
- Whisper model compatibility

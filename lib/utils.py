import numpy as np
from sklearn.metrics.pairwise import cosine_distances

def cosine_distance(x, y):
    """
    Compute the cosine distance between two embeddings.
    x, y: numpy arrays with the same dimensions.
    """
    x = np.array(x)
    y = np.array(y)
    if x.ndim == 1:
        x = x.reshape(1, -1)
    if y.ndim == 1:
        y = y.reshape(1, -1)
    return float(cosine_distances(x, y)[0][0])

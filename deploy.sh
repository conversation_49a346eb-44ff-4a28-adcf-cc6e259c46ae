#!/bin/bash

# Simple RunPod Deployment Script for Whisper Diarization

set -e

IMAGE_NAME="whisper-diarization"
REGISTRY="your-registry"  # Change this to your Docker registry

echo "🚀 Building and deploying Whisper Diarization to RunPod..."

# Check environment
if [ -z "$HUGGINGFACE_TOKEN" ]; then
    echo "⚠️  Warning: HUGGINGFACE_TOKEN not set"
    echo "Set it with: export HUGGINGFACE_TOKEN=your_token"
fi

# Build image
echo "🔨 Building Docker image..."
docker build -t $IMAGE_NAME .

# Tag for registry
echo "🏷️  Tagging image..."
docker tag $IMAGE_NAME $REGISTRY/$IMAGE_NAME:latest

echo "✅ Build complete!"
echo ""
echo "📋 Next steps:"
echo "1. Push to registry: docker push $REGISTRY/$IMAGE_NAME:latest"
echo "2. Create RunPod endpoint with image: $REGISTRY/$IMAGE_NAME:latest"
echo "3. Set HUGGINGFACE_TOKEN environment variable in RunPod"
echo "4. Test with test_input.json"
echo ""
echo "🧪 Test locally first:"
echo "docker run --rm -p 8000:8000 -e HUGGINGFACE_TOKEN=\$HUGGINGFACE_TOKEN $IMAGE_NAME"

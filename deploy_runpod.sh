#!/bin/bash

# RunPod Deployment Script for Whisper Diarization

set -e

# Configuration
IMAGE_NAME="whisper-diarization-runpod"
REGISTRY_URL=""  # Set your container registry URL here
TAG="latest"

echo "🚀 RunPod Whisper Diarization Deployment Script"
echo "================================================"

# Check if HuggingFace token is set
if [ -z "$HUGGINGFACE_TOKEN" ]; then
    echo "❌ ERROR: HUGGINGFACE_TOKEN environment variable is not set"
    echo "Please set your HuggingFace token:"
    echo "export HUGGINGFACE_TOKEN='your_token_here'"
    echo ""
    echo "You need access to:"
    echo "- pyannote/segmentation-3.0"
    echo "- pyannote/speaker-diarization-3.1"
    exit 1
fi

echo "✅ HuggingFace token is set"

# Check if registry URL is set
if [ -z "$REGISTRY_URL" ]; then
    echo "❌ ERROR: Please set REGISTRY_URL in this script"
    echo "Example: REGISTRY_URL='your-registry.com/your-username'"
    exit 1
fi

# Build Docker image
echo "🔨 Building Docker image..."
docker build -t $IMAGE_NAME:$TAG .

if [ $? -ne 0 ]; then
    echo "❌ Docker build failed"
    exit 1
fi

echo "✅ Docker image built successfully"

# Tag for registry
FULL_IMAGE_NAME="$REGISTRY_URL/$IMAGE_NAME:$TAG"
echo "🏷️  Tagging image as $FULL_IMAGE_NAME"
docker tag $IMAGE_NAME:$TAG $FULL_IMAGE_NAME

# Push to registry
echo "📤 Pushing to registry..."
docker push $FULL_IMAGE_NAME

if [ $? -ne 0 ]; then
    echo "❌ Docker push failed"
    exit 1
fi

echo "✅ Image pushed successfully"

echo ""
echo "🎉 Deployment preparation complete!"
echo "================================================"
echo "Image: $FULL_IMAGE_NAME"
echo ""
echo "Next steps:"
echo "1. Go to RunPod and create a new template"
echo "2. Use the image: $FULL_IMAGE_NAME"
echo "3. Set environment variable: HUGGINGFACE_TOKEN"
echo "4. Configure GPU requirements (RTX 4090 or better recommended)"
echo "5. Deploy your endpoint"
echo ""
echo "API Usage:"
echo "POST to your RunPod endpoint with:"
echo '{'
echo '  "input": {'
echo '    "file_string": "base64_encoded_audio",'
echo '    "num_speakers": 2,'
echo '    "enable_merge": false'
echo '  }'
echo '}'

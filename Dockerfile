# RunPod Serverless Dockerfile for Whisper Diarization
FROM nvidia/cuda:12.3.2-cudnn9-runtime-ubuntu22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3.10 \
    python3.10-dev \
    python3-pip \
    ffmpeg \
    git \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && ln -s /usr/bin/python3.10 /usr/bin/python

# Set working directory
WORKDIR /

# Copy and install requirements
COPY runpod_requirements.txt .
RUN pip install --no-cache-dir torch==2.3.1 torchaudio --index-url https://download.pytorch.org/whl/cu121
RUN pip install --no-cache-dir -r runpod_requirements.txt

# Copy application code
COPY lib/ /lib/
COPY handler.py /handler.py
COPY test_input.json /test_input.json

# Set Python path
ENV PYTHONPATH=/

# Command to run the handler
CMD ["python", "handler.py"]

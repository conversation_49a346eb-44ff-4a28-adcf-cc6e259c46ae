# lib/merge.py
import re
import numpy as np
import pandas as pd
from lib.utils import cosine_distance

class Merge:
    def process(self, segments, diarize_segments, speaker_emb_map):
        """
        Merges transcription segments with diarization results using cosine distance.
        
        For each transcription segment:
          1. Collect overlapping diarization segments.
          2. Calculate an average embedding from these overlapping segments.
          3. Assign a speaker by comparing the averaged embedding to reference speaker embeddings 
             using cosine_distance.
          4. If no valid embedding is available, use a fallback majority vote of overlapping diarization 
             segments' speaker labels.
        
        After assigning a speaker to each segment, the segments are sorted by their start time and
        then merged—only contiguous segments with the same speaker are combined.
        
        The output is a list of segments, in timeline order, where each segment represents one speaker's
        contiguous speaking block.
        """
        final_segments = []

        # Iterate over each transcription segment.
        for segment in segments:
            if "words" not in segment:
                continue

            overlapping_embeddings = []
            # Collect embeddings from diarization segments with temporal overlap.
            for d_seg in diarize_segments:
                # Calculate the temporal overlap between the transcription segment and the diarization segment.
                overlap = min(segment["end"], d_seg["end"]) - max(segment["start"], d_seg["start"])
                if overlap > 0:
                    if "embeddings" in d_seg and d_seg["embeddings"].size > 0:
                        emb = np.mean(d_seg["embeddings"], axis=0)
                        overlapping_embeddings.append(emb)
            
            # Determine the speaker based on overlapping embeddings.
            if overlapping_embeddings:
                # Compute the average embedding for this transcription segment.
                segment_embedding = np.mean(np.vstack(overlapping_embeddings), axis=0)
                if speaker_emb_map:
                    min_distance = float('inf')
                    assigned_speaker = None
                    # Compare against each reference embedding.
                    for speaker, ref_emb in speaker_emb_map.items():
                        ref_emb = np.array(ref_emb)
                        try:
                            distance = cosine_distance(segment_embedding, ref_emb)
                            if distance < min_distance:
                                min_distance = distance
                                assigned_speaker = speaker
                        except Exception as e:
                            print(f"Error computing cosine distance: {e}")
                    # Fallback if no assignment was made.
                    if assigned_speaker is None:
                        assigned_speaker = "UNKNOWN"
                    segment["speaker"] = assigned_speaker
                else:
                    segment["speaker"] = "UNKNOWN"
            else:
                # Fallback: use majority vote of overlapping diarization segments' labels.
                overlapping_labels = []
                for d_seg in diarize_segments:
                    overlap = min(segment["end"], d_seg["end"]) - max(segment["start"], d_seg["start"])
                    if overlap > 0 and d_seg.get("speaker", "UNKNOWN") != "UNKNOWN":
                        overlapping_labels.append(d_seg["speaker"])
                if overlapping_labels:
                    assigned_speaker = max(set(overlapping_labels), key=overlapping_labels.count)
                    segment["speaker"] = assigned_speaker
                else:
                    segment["speaker"] = "UNKNOWN"

            final_segments.append(segment)

        # Sort the segments in timeline order by their start time.
        final_segments = sorted(final_segments, key=lambda s: s["start"])

        # Merge contiguous segments that have the same speaker.
        merged_segments = []
        current_segment = final_segments[0].copy()
        for seg in final_segments[1:]:
            if seg["speaker"] == current_segment["speaker"]:
                # Merge the segments by extending the end time, concatenating text, and merging word lists.
                current_segment["end"] = seg["end"]
                current_segment["text"] += " " + seg["text"]
                current_segment["words"].extend(seg["words"])
            else:
                merged_segments.append(current_segment)
                current_segment = seg.copy()
        merged_segments.append(current_segment)

        # Clean up the merged segments: remove extra whitespaces and calculate duration.
        for seg in merged_segments:
            seg["text"] = re.sub(r"\s+", " ", seg["text"]).strip()
            seg["text"] = re.sub(r"\s+([.,!?])", r"\1", seg["text"])
            seg["duration"] = seg["end"] - seg["start"]

        return merged_segments

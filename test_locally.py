import torch
from torch.cuda.amp import autocast
import warnings
from contextlib import contextmanager
import json
import os


warnings.filterwarnings("ignore")


@contextmanager
def no_grad_context():
    with torch.no_grad():
        yield


##############################################################################
# Main-Block: VAD-Segmentierung und weitere Verarbeitung
##############################################################################
if __name__ == "__main__":
    file_path = "test/normalized.wav"
    transcribe_cache = "test/transcribe.json"
    diarize_cache = "test/diarize.json"

    try:
        from lib.audio_utils import split_audio_from_segments
        from lib.diarization_v1 import DiarizationProcessor
        from lib.transcribe import Transcribe
        from lib.merge import Merge

        # Load or generate transcription results
        if os.path.exists(transcribe_cache):
            print("Loading transcription from cache...")
            with open(transcribe_cache, "r", encoding="utf-8") as f:
                cached_data = json.load(f)
                segments = cached_data["segments"]
        else:
            print("Generating new transcription...")
            transcribe = Transcribe()
            segments, transcript_info = transcribe.speech_to_text(file_path)
            
            # Save transcription results
            with open(transcribe_cache, "w", encoding="utf-8") as f:
                json.dump({
                    "segments": segments
                }, f, indent=2, ensure_ascii=False)

        # Load or generate diarization results
        if os.path.exists(diarize_cache):
            print("Loading diarization from cache...")
            with open(diarize_cache, "r", encoding="utf-8") as f:
                diarize_segments = json.load(f)
            speaker_emb_map = {}
            
        else:
            print("Generating new diarization...")
            # Initialize diarizer
            diarizer = DiarizationProcessor()
            # Process the file
            diarize_segments, num_speakers, speaker_labels, speaker_emb_map = diarizer.process(file_path)

            # Save diarization results
            with open(diarize_cache, "w", encoding="utf-8") as f:
                json.dump(diarize_segments, f, indent=2, ensure_ascii=False)


        merge = Merge()

        # Merge segments
        merged_segments = merge.process(segments, diarize_segments, speaker_emb_map)
        print(f"Processed {len(merged_segments)} merged segments")
        
        # Print in alternating speaker format
        for segment in merged_segments:
            print(f"\nSpeaker {segment['speaker']}:")
            print(segment['text'])
        
    except Exception as e:
        print(f"Error during processing: {e}")
        raise

    exit(0)  
    # Verarbeite alle durch VAD segmentierten Audiodaten
    # process_audio_segments(loaded_audio_segments)

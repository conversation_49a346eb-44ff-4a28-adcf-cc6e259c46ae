#!/bin/bash

# RunPod Serverless Deployment Script for Whisper Diarization
# This script builds and deploys the Whisper Diarization service to RunPod Serverless

set -e

# Configuration
IMAGE_NAME="whisper-diarization-runpod"
TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${TAG}"

echo "🚀 Starting RunPod Serverless deployment for Whisper Diarization..."

# Check if required environment variables are set
if [ -z "$RUNPOD_API_KEY" ]; then
    echo "❌ Error: RUNPOD_API_KEY environment variable is not set"
    echo "Please set your RunPod API key: export RUNPOD_API_KEY=your_api_key_here"
    exit 1
fi

if [ -z "$HUGGINGFACE_TOKEN" ]; then
    echo "⚠️  Warning: HUGGINGFACE_TOKEN environment variable is not set"
    echo "This is required for pyannote models. Set it with: export HUGGINGFACE_TOKEN=your_token_here"
fi

# Build Docker image
echo "🔨 Building Docker image..."
docker build -f Dockerfile.runpod -t $FULL_IMAGE_NAME .

if [ $? -eq 0 ]; then
    echo "✅ Docker image built successfully: $FULL_IMAGE_NAME"
else
    echo "❌ Docker build failed"
    exit 1
fi

# Test the image locally (optional)
echo "🧪 Testing image locally..."
echo "You can test the image with:"
echo "docker run --rm -p 8000:8000 -e HUGGINGFACE_TOKEN=\$HUGGINGFACE_TOKEN $FULL_IMAGE_NAME"
echo ""

# Instructions for RunPod deployment
echo "📋 Next steps for RunPod Serverless deployment:"
echo ""
echo "1. Push your image to a container registry (Docker Hub, ECR, etc.):"
echo "   docker tag $FULL_IMAGE_NAME your-registry/$FULL_IMAGE_NAME"
echo "   docker push your-registry/$FULL_IMAGE_NAME"
echo ""
echo "2. Create a new RunPod Serverless endpoint:"
echo "   - Go to https://www.runpod.io/console/serverless"
echo "   - Click 'New Endpoint'"
echo "   - Use your pushed image URL"
echo "   - Set environment variables:"
echo "     - HUGGINGFACE_TOKEN: your_huggingface_token"
echo "   - Configure GPU requirements (recommend RTX 4090 or A100)"
echo "   - Set timeout and scaling settings"
echo ""
echo "3. Test your endpoint with the provided test_input.json"
echo ""
echo "🎉 Deployment preparation complete!"

# Optional: Run local test
read -p "Do you want to run a local test? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧪 Running local test..."
    if [ -n "$HUGGINGFACE_TOKEN" ]; then
        docker run --rm -p 8000:8000 -e HUGGINGFACE_TOKEN=$HUGGINGFACE_TOKEN $FULL_IMAGE_NAME &
        DOCKER_PID=$!
        
        echo "Waiting for container to start..."
        sleep 10
        
        echo "Testing with curl..."
        curl -X POST http://localhost:8000/runsync \
             -H "Content-Type: application/json" \
             -d @test_input.json
        
        echo "Stopping container..."
        kill $DOCKER_PID
    else
        echo "⚠️  Skipping local test - HUGGINGFACE_TOKEN not set"
    fi
fi

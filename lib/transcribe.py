import time
import torch
from faster_whisper.vad import VadOptions
from faster_whisper import WhisperModel

class Transcribe:

    def __init__(self):
        """Load models into memory"""
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model = WhisperModel(
            "large-v3-turbo",
            device=self.device,
            compute_type="float16" if self.device == "cuda" else "int8",
        )

    def speech_to_text(self, audio_file_wav, num_speakers=None, prompt="", language=None, translate=False, vad_filter=True):
        """
        Transcribes the given WAV audio file using the provided Whisper model.
        
        Returns:
        - A list of segments, each with start/end times, text, and word-level timestamps.
        - Transcript meta-information (e.g. detected language).
        """
        time_start = time.time()
        print("Starting transcription")
        options = dict(
            language=language,
            beam_size=5,
            vad_filter=vad_filter,
            vad_parameters=VadOptions(
                max_speech_duration_s=60.0,
                min_speech_duration_ms=100,
                speech_pad_ms=300,
                threshold=0.15,
                neg_threshold=0.1,
            ),
            word_timestamps=True,
            initial_prompt=prompt,
            language_detection_segments=1,
            task="translate" if translate else "transcribe",
        )
        segments, transcript_info = self.model.transcribe(audio_file_wav, **options)
        segments = list(segments)
        segments = [
            {
                "avg_logprob": s.avg_logprob,
                "start": float(s.start),
                "end": float(s.end),
                "text": s.text,
                "words": [
                    {
                        "start": float(w.start),
                        "end": float(w.end),
                        "word": w.word,
                        "probability": w.probability,
                    }
                    for w in s.words
                ],
            }
            for s in segments
        ]
        time_transcription_end = time.time()
        print(f"Finished transcription in {time_transcription_end - time_start:.5f} seconds, {len(segments)} segments")
        return segments, transcript_info

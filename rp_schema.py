"""
RunPod Input Schema for Whisper Diarization Service

This schema defines the input validation for the Whisper Diarization RunPod handler.
It supports audio input via URL, base64, or file path, along with various transcription
and diarization parameters.
"""

INPUT_VALIDATIONS = {
    # Audio input options (only one should be provided)
    'file_string': {
        'type': str,
        'required': False,
        'default': None,
        'description': 'Base64 encoded audio file'
    },
    'file_url': {
        'type': str,
        'required': False,
        'default': None,
        'description': 'URL to audio file'
    },
    'file': {
        'type': str,
        'required': False,
        'default': None,
        'description': 'Local file path to audio file'
    },
    
    # Transcription parameters
    'translate': {
        'type': bool,
        'required': False,
        'default': False,
        'description': 'Whether to translate to English'
    },
    'language': {
        'type': str,
        'required': False,
        'default': None,
        'description': 'Language code for transcription (auto-detect if None)'
    },
    'prompt': {
        'type': str,
        'required': False,
        'default': '',
        'description': 'Initial prompt for Whisper model'
    },
    'vad_filter': {
        'type': bool,
        'required': False,
        'default': True,
        'description': 'Enable Voice Activity Detection filtering'
    },
    
    # Diarization parameters
    'num_speakers': {
        'type': int,
        'required': False,
        'default': None,
        'description': 'Number of speakers (auto-detect if None)'
    },
    'known_speakers': {
        'type': dict,
        'required': False,
        'default': None,
        'description': 'Dictionary of known speaker embeddings'
    },
    
    # Audio processing
    'normalize_audio': {
        'type': bool,
        'required': False,
        'default': True,
        'description': 'Whether to normalize audio before processing'
    },
    
    # Processing options
    'diarize_only': {
        'type': bool,
        'required': False,
        'default': False,
        'description': 'Only perform diarization, skip transcription'
    },
    'enable_merge': {
        'type': bool,
        'required': False,
        'default': False,
        'description': 'Whether to merge whisper and diarization results'
    },
    
    # Debug and output options
    'debug_info': {
        'type': bool,
        'required': False,
        'default': False,
        'description': 'Include debug information in output'
    }
}

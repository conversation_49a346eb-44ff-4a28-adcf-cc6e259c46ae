import base64
import subprocess
import os
import requests
import time
import torch
from typing import List, Dict, <PERSON><PERSON>

def download_file(file_url):
    temp_audio_filename = f"temp-{time.time_ns()}.audio"
    retry_count = 0
    max_retries = 3
    while retry_count < max_retries:
        try:
            response = requests.get(file_url)
            with open(temp_audio_filename, "wb") as f:
                f.write(response.content)
            return temp_audio_filename
        except Exception as e:
            retry_count += 1
            if retry_count >= max_retries:
                raise e
            time.sleep(10)
            continue


def get_wave_file(file_string, file_url, file, normalize_audio=False, use_cuda=False):
    """
    Converts the input audio (from Base64, URL, or file) to a WAV file using ffmpeg.
    """
    temp_wav_filename = f"temp-{time.time_ns()}.wav"
    ffmpeg_base = ["ffmpeg"]
    if use_cuda:
        ffmpeg_base += ["-hwaccel", "cuda"]
    ffmpeg_base += ["-i"]
    conversion_params = ["-ar", "16000", "-ac", "1"]

    if normalize_audio:
        # conversion_params += ["-af", "dynaudnorm"]
        # conversion_params += ["-af", "acompressor=threshold=-20dB:ratio=4:attack=200:release=1000, loudnorm=I=-16:TP=-1.5:LRA=11"]
        conversion_params += ["-af", "speechnorm=e=12.5:r=0.0001:l=1"]
    

    conversion_params += ["-c:a", "pcm_s16le", temp_wav_filename]
    if file is not None:
        temp_audio_filename = file
    elif file_url is not None:
        temp_audio_filename = download_file(file_url)
    elif file_string is not None:
        audio_data = base64.b64decode(file_string.split(",")[1] if "," in file_string else file_string)
        temp_audio_filename = f"temp-{time.time_ns()}.audio"
        with open(temp_audio_filename, "wb") as f:
            f.write(audio_data)
    else:
        raise RuntimeError("No valid audio file provided.")
    
    convert_retry_count = 0
    max_convert_retries = 3
    while convert_retry_count < max_convert_retries:
        try:
            subprocess.run(ffmpeg_base + [temp_audio_filename] + conversion_params, check=True)
            break
        except Exception as e:
            convert_retry_count += 1
            if convert_retry_count >= max_convert_retries:
                raise e
            time.sleep(10)
            continue
        finally:
            if os.path.exists(temp_audio_filename):
                os.remove(temp_audio_filename)

    return temp_wav_filename


def split_audio_from_segments( waveform: torch.Tensor,
                            sample_rate: int,
                        speech_timestamps: List[Dict[str, float]]) -> List[Tuple[torch.Tensor, int, str]]:
    """
    Extracts waveform segments based on speech timestamps.
    
    Args:
        waveform (torch.Tensor): Original audio waveform
        sample_rate (int): Audio sample rate
        speech_timestamps (List[Dict]): List of speech segments with start/end times
        
    Returns:
        List[Tuple[torch.Tensor, int, str]]: List of (segment_waveform, sample_rate, identifier)
    """
    segments = []
    
    for idx, segment in enumerate(speech_timestamps):
        start_sample = int(segment['start'] * sample_rate)
        end_sample = int(segment['end'] * sample_rate)
        
        # Extract segment waveform
        segment_waveform = waveform[:, start_sample:end_sample]
        
        # Create unique identifier for this segment
        identifier = f"segment_{idx}"
        
        segments.append((segment_waveform, sample_rate, identifier, segment))
        
    return segments
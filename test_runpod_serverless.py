#!/usr/bin/env python3
"""
Local test script for RunPod Serverless Whisper Diarization

This script allows you to test the RunPod handler locally before deployment.
"""

import json
import os
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from rp_handler import run_whisper_diarization_job

def test_with_url():
    """Test with audio URL"""
    print("🧪 Testing with audio URL...")
    
    job = {
        "id": "test-job-url",
        "input": {
            "file_url": "https://github.com/ggerganov/whisper.cpp/raw/master/samples/jfk.wav",
            "num_speakers": 2,
            "language": "en",
            "enable_merge": True,
            "debug_info": True
        }
    }
    
    result = run_whisper_diarization_job(job)
    print("✅ URL test result:")
    print(json.dumps(result, indent=2))
    return result

def test_with_local_file():
    """Test with local audio file"""
    print("\n🧪 Testing with local file...")
    
    # Check if test audio file exists
    test_file = "test/normalized.wav"
    if not os.path.exists(test_file):
        print(f"⚠️  Test file {test_file} not found, skipping local file test")
        return None
    
    job = {
        "id": "test-job-local",
        "input": {
            "file": test_file,
            "num_speakers": None,  # Auto-detect
            "language": None,      # Auto-detect
            "enable_merge": True,
            "vad_filter": True,
            "normalize_audio": True
        }
    }
    
    result = run_whisper_diarization_job(job)
    print("✅ Local file test result:")
    print(json.dumps(result, indent=2))
    return result

def test_diarization_only():
    """Test diarization-only mode"""
    print("\n🧪 Testing diarization-only mode...")
    
    job = {
        "id": "test-job-diarize-only",
        "input": {
            "file_url": "https://github.com/ggerganov/whisper.cpp/raw/master/samples/jfk.wav",
            "diarize_only": True,
            "num_speakers": 2
        }
    }
    
    result = run_whisper_diarization_job(job)
    print("✅ Diarization-only test result:")
    print(json.dumps(result, indent=2))
    return result

def test_validation_errors():
    """Test input validation"""
    print("\n🧪 Testing input validation...")
    
    # Test with no audio input
    job = {
        "id": "test-job-validation",
        "input": {
            "num_speakers": 2
        }
    }
    
    result = run_whisper_diarization_job(job)
    print("✅ Validation test result (should show error):")
    print(json.dumps(result, indent=2))
    
    # Test with multiple audio inputs
    job = {
        "id": "test-job-validation-2",
        "input": {
            "file_url": "https://example.com/audio.wav",
            "file_string": "base64data",
            "num_speakers": 2
        }
    }
    
    result = run_whisper_diarization_job(job)
    print("✅ Multiple input validation test result (should show error):")
    print(json.dumps(result, indent=2))
    
    return result

def main():
    """Run all tests"""
    print("🚀 Starting RunPod Serverless Whisper Diarization Tests")
    print("=" * 60)
    
    # Check environment
    hf_token = os.environ.get("HUGGINGFACE_TOKEN")
    if not hf_token:
        print("⚠️  WARNING: HUGGINGFACE_TOKEN not set. Some tests may fail.")
        print("Set it with: export HUGGINGFACE_TOKEN=your_token_here")
    else:
        print("✅ HUGGINGFACE_TOKEN is set")
    
    print("\n" + "=" * 60)
    
    try:
        # Run tests
        test_validation_errors()
        
        # Only run actual processing tests if HF token is available
        if hf_token:
            test_with_url()
            test_with_local_file()
            test_diarization_only()
        else:
            print("\n⚠️  Skipping processing tests due to missing HUGGINGFACE_TOKEN")
        
        print("\n🎉 All tests completed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())

#!/usr/bin/env python3
"""
Test script for RunPod handler locally
"""
import os
import json
import base64
from handler import WhisperDiar<PERSON><PERSON>and<PERSON>

def encode_audio_file(file_path):
    """Encode audio file to base64"""
    with open(file_path, "rb") as f:
        return base64.b64encode(f.read()).decode('utf-8')

def test_handler():
    """Test the RunPod handler locally"""
    
    # Initialize handler
    handler = WhisperDiarizationHandler()
    
    # Test with a sample audio file (adjust path as needed)
    audio_file = "test/normalized.wav"  # Adjust this path
    
    if not os.path.exists(audio_file):
        print(f"Audio file {audio_file} not found. Please provide a valid audio file.")
        return
    
    # Encode audio file
    print("Encoding audio file...")
    file_string = encode_audio_file(audio_file)
    
    # Create test job
    test_job = {
        "input": {
            "file_string": file_string,
            "num_speakers": 2,
            "translate": False,
            "language": "en",
            "enable_merge": False,  # Test with merge disabled
            "debug_info": True
        }
    }
    
    print("Processing request...")
    result = handler.process_request(test_job)
    
    # Save results
    output_file = "test_runpod_output.json"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"Results saved to {output_file}")
    
    # Print summary
    if "error" in result:
        print(f"Error: {result['error']}")
    else:
        print(f"Whisper segments: {len(result.get('whisper_segments', []))}")
        print(f"Diarization segments: {len(result.get('diarization_segments', []))}")
        print(f"Language: {result.get('language')}")
        print(f"Number of speakers: {result.get('num_speakers')}")
        print(f"Merged segments: {len(result.get('merged_segments', []))}")

def test_with_merge():
    """Test with merge enabled"""
    
    handler = WhisperDiarizationHandler()
    
    audio_file = "test/normalized.wav"
    
    if not os.path.exists(audio_file):
        print(f"Audio file {audio_file} not found.")
        return
    
    file_string = encode_audio_file(audio_file)
    
    test_job = {
        "input": {
            "file_string": file_string,
            "num_speakers": 2,
            "enable_merge": True,  # Test with merge enabled
            "debug_info": False
        }
    }
    
    print("Testing with merge enabled...")
    result = handler.process_request(test_job)
    
    output_file = "test_runpod_output_merged.json"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"Merged results saved to {output_file}")

if __name__ == "__main__":
    print("Testing RunPod Handler Locally")
    print("=" * 40)
    
    # Test without merge
    test_handler()
    
    print("\n" + "=" * 40)
    
    # Test with merge
    test_with_merge()

name: Docker Image

on:
  push:
    branches:
      - "main"

jobs:
  docker:
    runs-on: DO
    steps:
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          push: true
          tags: ${{ vars.DOCKERHUB_REPO }}/${{ vars.DOCKERHUB_IMG }}:dev
      - uses: actions/checkout@v3
      - name: Run Tests
        uses: direlines/runpod-test-runner@v1.7
        with:
          image-tag: ${{ vars.DOCKERHUB_REPO }}/${{ vars.DOCKERHUB_IMG }}:dev
          runpod-api-key: ${{ secrets.RUNPOD_API_KEY }}
          request-timeout: 600

import re

class Merge:
    def process(self, segments, diarize_segments, speaker_emb_map=None):
        """
        Merges transcription segments with diarization results.

        For each transcription segment:
          1. Determine the speaker label by finding diarization segments that overlap
             and choosing the speaker with the highest total overlap duration.
          2. Assign each word a speaker similarly, falling back to the segment speaker.

        After assigning a speaker to each segment, contiguous segments by the same speaker
        are merged under reasonable conditions to form coherent blocks.
        """
        final_segments = []

        # Assign a speaker to each segment and its words based on temporal overlap
        for segment in segments:
            if not segment.get("words"):
                continue

            # Compute overlap durations for segment-level assignment
            overlaps = {}
            for d in diarize_segments:
                start = max(segment["start"], d["start"])
                end = min(segment["end"], d["end"])
                duration = end - start
                if duration > 0:
                    overlaps[d.get("speaker", "UNKNOWN")] = overlaps.get(d.get("speaker", "UNKNOWN"), 0) + duration

            # Choose speaker with max overlap
            if overlaps:
                speaker = max(overlaps, key=overlaps.get)
            else:
                speaker = "UNKNOWN"

            # Assign speakers to words
            words_with_speaker = []
            for w in segment["words"]:
                word_overlaps = {}
                for d in diarize_segments:
                    start = max(w["start"], d["start"])
                    end = min(w["end"], d["end"])
                    duration = end - start
                    if duration > 0:
                        word_overlaps[d.get("speaker", "UNKNOWN")] = word_overlaps.get(d.get("speaker", "UNKNOWN"), 0) + duration
                if word_overlaps:
                    w_speaker = max(word_overlaps, key=word_overlaps.get)
                else:
                    w_speaker = speaker
                w["speaker"] = w_speaker
                words_with_speaker.append(w)

            final_segments.append({
                "start": segment["start"],
                "end": segment["end"],
                "text": segment.get("text", ""),
                "speaker": speaker,
                "words": words_with_speaker
            })

        # If no segments, return empty
        if not final_segments:
            return []

        # Sort by start time
        final_segments.sort(key=lambda x: x["start"])

        # Merge consecutive segments by same speaker
        merged = []
        current = final_segments[0].copy()

        for seg in final_segments[1:]:
            if seg["speaker"] == current["speaker"]:
                # Merge the segments
                current["end"] = seg["end"]
                current["text"] = current["text"].rstrip() + " " + seg["text"].lstrip()
                current["words"].extend(seg["words"])
            else:
                merged.append(current)
                current = seg.copy()
        merged.append(current)

        # Cleanup: normalize whitespace and punctuation spacing
        for m in merged:
            m["text"] = re.sub(r"\s+", " ", m["text"]).strip()
            m["text"] = re.sub(r"\s+([.,!?])", r"\1", m["text"])
            m["duration"] = m["end"] - m["start"]

        return merged

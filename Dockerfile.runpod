# RunPod Serverless Dockerfile for Whisper Diarization
# Based on the faster-whisper RunPod example with adaptations for diarization

FROM nvidia/cuda:12.3.2-cudnn9-runtime-ubuntu22.04

# Remove any third-party apt sources to avoid issues with expiring keys
RUN rm -f /etc/apt/sources.list.d/*.list

# Set shell and noninteractive environment variables
SHELL ["/bin/bash", "-c"]
ENV DEBIAN_FRONTEND=noninteractive
ENV SHELL=/bin/bash
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Set working directory
WORKDIR /

# Update and upgrade the system packages
RUN apt-get update -y && \
    apt-get upgrade -y && \
    apt-get install --yes --no-install-recommends \
        sudo \
        ca-certificates \
        git \
        wget \
        curl \
        bash \
        libgl1 \
        libx11-6 \
        software-properties-common \
        ffmpeg \
        build-essential \
        libmagic1 \
        python3.10 \
        python3.10-dev \
        python3.10-venv \
        python3-pip -y && \
    ln -s /usr/bin/python3.10 /usr/bin/python && \
    rm -f /usr/bin/python3 && \
    ln -s /usr/bin/python3.10 /usr/bin/python3 && \
    apt-get autoremove -y && \
    apt-get clean -y && \
    rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY runpod_requirements.txt /requirements.txt
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --upgrade pip && \
    pip install huggingface_hub[hf_xet] && \
    pip install -r /requirements.txt --no-cache-dir

# Copy application code
COPY lib/ /lib/
COPY rp_handler.py /rp_handler.py
COPY rp_schema.py /rp_schema.py

# Test input that will be used when the container runs outside of runpod
COPY test_input.json /test_input.json

# Set Python path
ENV PYTHONPATH=/

# Set default command
CMD python -u /rp_handler.py

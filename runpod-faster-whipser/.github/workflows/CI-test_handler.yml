name: <PERSON><PERSON> | Test Worker

on:
  push:
    branches:
      - main

  pull_request:
    branches:
      - main

  workflow_dispatch:

jobs:
  initialize_worker:
    runs-on: ubuntu-latest
    outputs:
      id: ${{ steps.extract_id.outputs.runpod_job_id }}

    steps:
      - name: Deploy Worker
        id: deploy
        uses: fjogeleit/http-request-action@v1
        with:
          url: "https://api.runpod.ai/v2/${{ secrets.RUNPOD_ENDPOINT }}/run"
          method: "POST"
          customHeaders: '{"Content-Type": "application/json"}'
          bearerToken: ${{ secrets.RUNPOD_API_KEY }}
          data: '{"input":{"github_pat": "${{ secrets.GH_PAT }}", "github_org":"${{ secrets.GH_ORG }}"}}'

      - name: Extract Job ID
        id: extract_id
        run: |
          ID=$(echo '${{ steps.deploy.outputs.response }}' | jq -r '.id')
          echo "::set-output name=runpod_job_id::$ID"

  run_tests:
    needs: initialize_worker
    runs-on: runpod

    steps:
      - uses: actions/checkout@v3

      - name: Set up environment
        run: |
          rm -f /etc/apt/sources.list.d/*.list
          apt-get update -y
          apt-get upgrade -y
          apt-get install --yes --no-install-recommends sudo ca-certificates git wget curl bash libgl1 libx11-6 software-properties-common ffmpeg build-essential -y
          apt-get autoremove -y
          apt-get clean -y
          rm -rf /var/lib/apt/lists/*

      - name: Set up Python 3.10 & install dependencies
        uses: actions/setup-python@v4
        with:
          python-version: "3.10.12"

      - name: Install Dependencies
        run: |

          python -m pip install --upgrade pip
          pip install -r builder/requirements.txt

      - name: Fetch and run models
        run: |
          python builder/fetch_models.py

      - name: Execute Tests
        run: |
          python src/rp_handler.py --test_input='{"input": {"audio": "https://github.com/runpod-workers/sample-inputs/raw/main/audio/gettysburg.wav"}}'

  terminate_worker:
    if: ${{ always() && !success() }}
    needs: initialize_worker
    runs-on: ubuntu-latest

    steps:
      - name: Shutdown Worker
        uses: fjogeleit/http-request-action@v1
        with:
          url: "https://api.runpod.ai/v2/${{ secrets.RUNPOD_ENDPOINT }}/cancel/${{ needs.initialize_worker.outputs.id }}"
          method: "POST"
          customHeaders: '{"Content-Type": "application/json"}'
          bearerToken: ${{ secrets.RUNPOD_API_KEY }}
